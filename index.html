<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width,initial-scale=1" />
	<title>Modulan Poster</title>
	<link rel="stylesheet" href="style.css" />
	<!-- If you have the licensed YWFT Modulan font, place it in /fonts and uncomment below -->
	<!-- <link rel="preload" href="fonts/YWFTModulan.woff2" as="font" type="font/woff2" crossorigin> -->
</head>
<body>
	<div id="canvas-wrapper">
		<main id="canvas" role="img" aria-label="Modern typographic poster for Modulan typeface">
      <!-- Top Left Branding -->
      <div class="branding">
        <div class="logo" aria-hidden="true"><span class="logo-inner"></span></div>
        <div class="brand-text">YWFT Modulan</div>
      </div>
      
      <!-- Hamburger Menu -->
      <div class="hamburger" aria-hidden="true">
        <span></span><span></span><span></span>
      </div>
      
      <div class="aa" aria-hidden="true">Aa</div>			<!-- Top Right Tagline & Pill -->
			<div class="tagline">A Sans Serif Typeface</div>
			<button class="pill" aria-label="Typeface classification tag">Sans Serif <span aria-hidden="true" class="pill-close">×</span></button>

			<!-- Central Hero Word -->
			<section class="hero" aria-label="Type specimen word Modulan with adjustment guides">
				<div class="word-group">
					<!-- Left Highlighted Segment with Guides -->
					<div class="guide-frame" aria-hidden="true">
						<div class="guide-vert left"></div>
						<div class="guide-vert right"></div>
						<div class="guide-dot top"></div>
						<div class="guide-dot bottom"></div>
						<div class="highlight highlight-left"></div>
					</div>
					<h1 class="word" aria-hidden="true">Modulan</h1>
					<!-- Right Highlight Box & Underline Bar -->
					<div class="highlight highlight-right" aria-hidden="true"></div>
					<div class="highlight-bar" aria-hidden="true"></div>
				</div>
			</section>

			<!-- Bottom Left Icon Row -->
			<div class="bottom-left-cluster" aria-label="Contact and media style icons">
				<div class="icon-row">
					<button class="icon circle phone" aria-label="Phone">
						<svg viewBox="0 0 24 24" width="40" height="40" aria-hidden="true"><path fill="#0b0b0b" d="M6.62 10.79a15.053 15.053 0 006.59 6.59l2.2-2.2a1 1 0 011.01-.24c1.12.37 2.33.57 3.58.57a1 1 0 011 1V21a1 1 0 01-1 1C10.42 22 2 13.58 2 3a1 1 0 011-1h3.5a1 1 0 011 1c0 1.25.2 2.46.57 3.58a1 1 0 01-.25 1.01l-2.2 2.2z"/></svg>
					</button>
					<button class="icon circle video" aria-label="Video">
						<svg viewBox="0 0 24 24" width="40" height="40" aria-hidden="true"><path fill="#0b0b0b" d="M3 6a2 2 0 012-2h9a2 2 0 012 2v2.382l3.553-2.132A1 1 0 0121 7.118v9.764a1 1 0 01-1.447.868L16 15.618V18a2 2 0 01-2 2H5a2 2 0 01-2-2V6z"/></svg>
					</button>
					<button class="icon circle play" aria-label="Play">
						<svg viewBox="0 0 24 24" width="40" height="40" aria-hidden="true"><path fill="#0b0b0b" d="M8 5v14l11-7-11-7z"/></svg>
					</button>
					<button class="icon circle chat" aria-label="Chat">
						<svg viewBox="0 0 24 24" width="40" height="40" aria-hidden="true"><path fill="#0b0b0b" d="M4 4h16a2 2 0 012 2v8a2 2 0 01-2 2h-6.5l-4.3 3.2A1 1 0 018 18.3V16H4a2 2 0 01-2-2V6a2 2 0 012-2z"/></svg>
					</button>
				</div>
				<div class="credit">Designed by YouWorkForThem</div>
			</div>

			<!-- Bottom Right Chat Bubble & Label -->
			<div class="bubble-cluster" aria-label="OpenType indicator">
				<div class="bubble" aria-hidden="true">
					<div class="dot"></div>
					<div class="dot"></div>
					<div class="dot"></div>
				</div>
				<div class="open-type">OpenType</div>
			</div>
		</main>
	</div>

	<script>
		// Maintain scale of 1440x900 layout in viewport
		(function(){
			const baseW=1440, baseH=900; const wrapper=document.getElementById('canvas-wrapper'); const canvas=document.getElementById('canvas');
			function res(){
				const sw=window.innerWidth, sh=window.innerHeight; const s=Math.min(sw/baseW, sh/baseH); canvas.style.transform='scale('+s+')'; canvas.style.width=baseW+'px'; canvas.style.height=baseH+'px'; wrapper.style.height=sh+'px'; }
			window.addEventListener('resize', res); res();
		})();
	</script>
</body>
</html>
