/* Poster Design Styles */
* { box-sizing:border-box; }
html,body { margin:0; height:100%; font-family: 'Inter', system-ui, sans-serif; background:#050505; color:#fff; -webkit-font-smoothing:antialiased; }
body:before { content:""; position:fixed; inset:0; pointer-events:none; background:repeating-radial-gradient(circle at 0 0, rgba(255,255,255,0.06) 0 1px, transparent 1px 6px); opacity:.07; }

:root {
	--cyan:#05b4d9;
	--cyan2:#0094ff;
	--blue-box:#063746;
	--blue-bar:#038bdf;
	--pink:#ff5ad5;
	--yellow:#ffd707;
	--video:#19c2e5;
	--chat:#0086dc;
	--canvas-w:1440px;
	--canvas-h:900px;
}

#canvas-wrapper { position:fixed; inset:0; display:flex; align-items:center; justify-content:center; overflow:hidden; }
#canvas { position:relative; width:var(--canvas-w); height:var(--canvas-h); transform-origin:top left; font-size:16px; }

/* Branding */
.branding { position:absolute; top:44px; left:84px; display:flex; align-items:center; gap:16px; }
.logo { width:36px; height:36px; border-radius:50%; background:#fff; position:relative; }
.logo-inner { position:absolute; inset:8px; background:#050505; border-radius:50%; display:block; }
.brand-text { font-size:18px; font-weight:500; }

/* Hamburger Menu */
.hamburger { position:absolute; top:138px; left:84px; display:flex; flex-direction:column; gap:11px; }
.hamburger span { display:block; width:48px; height:3px; background:var(--cyan); border-radius:2px; }

.aa { position:absolute; top:140px; left:148px; font-size:94px; font-weight:600; color:var(--cyan); line-height:1; }

.tagline { position:absolute; top:50px; right:80px; font-size:15px; letter-spacing:.3px; font-weight:400; }
.pill { position:absolute; top:170px; right:130px; background:transparent; border:2px solid var(--cyan); color:var(--cyan); font-size:30px; padding:18px 56px; border-radius:48px; font-weight:500; display:flex; align-items:center; gap:72px; cursor:default; } 
.pill-close { font-size:34px; line-height:1; }

/* Hero Word */
.hero { position:absolute; top:240px; left:84px; width:1270px; height:390px; }
.word-group { position:relative; width:100%; height:100%; }
.word { margin:0; font-weight:800; font-size:300px; letter-spacing:-6px; line-height:.8; font-family:'Inter', system-ui, sans-serif; position:relative; z-index:2; }

.guide-frame { position:absolute; top:40px; left:0; width:374px; height:288px; z-index:1; }
.guide-vert { position:absolute; top:0; width:6px; height:100%; background:var(--cyan); }
.guide-vert.left { left:0; }
.guide-vert.right { right:0; }
.guide-dot { position:absolute; width:40px; height:40px; background:var(--cyan); border-radius:50%; }
.guide-dot.top { top:-56px; left:-56px; }
.guide-dot.bottom { bottom:-56px; right:-56px; }

.highlight { position:absolute; z-index:1; }
.highlight-left { left:0; top:46px; width:374px; height:276px; background:#2d4a5c; }
.highlight-right { left:892px; top:52px; width:352px; height:272px; background:#2d4a5c; }
.highlight-bar { position:absolute; left:892px; bottom:-56px; width:352px; height:70px; background:var(--blue-bar); z-index:1; }

/* Icons Bottom Left */
.bottom-left-cluster { position:absolute; left:82px; bottom:104px; }
.icon-row { display:flex; gap:30px; }
.icon.circle { width:76px; height:76px; border-radius:50%; display:flex; align-items:center; justify-content:center; border:none; cursor:default; padding:0; }
.icon.circle svg { width:40px; height:40px; }
.icon.circle.phone { background:#ff59cc; }
.icon.circle.video { background:#04b4d8; }
.icon.circle.play { background:#ffd400; }
.icon.circle.chat { background:#0094ff; }
.credit { margin-top:34px; font-size:16px; letter-spacing:.2px; color:#d8d8d8; }

/* Bottom Right Elements */
.bubble { position:absolute; right:190px; top:610px; width:170px; height:118px; background:var(--cyan2); border-radius:14px; display:flex; align-items:center; justify-content:center; box-shadow:0 6px 10px -4px rgba(0,0,0,.6); }
.bubble .dot { width:28px; height:28px; background:#083d56; border-radius:50%; margin:0 11px; }
.open-type { position:absolute; right:122px; bottom:60px; font-size:17px; letter-spacing:.5px; font-weight:400; color:#ececec; }

/* Accessibility */
.pill:focus-visible, .icon.circle:focus-visible { outline:3px solid #fff; outline-offset:4px; }

/* Fallback if width too small: allow scroll */
@media (max-width:900px) { body { overflow:auto; } }
